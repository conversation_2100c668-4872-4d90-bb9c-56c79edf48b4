package services

import (
	"errors"
	"time"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

type UserService struct {
	userRepo     ports.UserRepository
	userTypeRepo ports.UserTypeRepository
	jwtSecret    string
}

func NewUserService(userRepo ports.UserRepository, userTypeRepo ports.UserTypeRepository, jwtSecret string) ports.UserService {
	return &UserService{
		userRepo:     userRepo,
		userTypeRepo: userTypeRepo,
		jwtSecret:    jwtSecret,
	}
}

func (s *UserService) Register(name, email, password string) (*domain.User, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if email == "" {
		return nil, errors.New("email is required")
	}
	if password == "" {
		return nil, errors.New("password is required")
	}
	if len(password) < 6 {
		return nil, errors.New("password must be at least 6 characters")
	}

	// Check if user already exists
	_, err := s.userRepo.FindByEmail(email)
	if err == nil {
		return nil, errors.New("user with this email already exists")
	}

	// Get default member user type
	memberTypeName := domain.UserTypeMember
	memberUserType, err := s.userTypeRepo.FindAll(&ports.UserTypeFilter{Name: &memberTypeName})
	if err != nil || len(memberUserType) == 0 {
		return nil, errors.New("default user type not found")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, errors.New("failed to hash password")
	}

	user := &domain.User{
		Name:       name,
		Email:      email,
		Password:   string(hashedPassword),
		UserTypeID: uint64(memberUserType[0].ID),
	}

	err = s.userRepo.Insert(user)
	if err != nil {
		return nil, err
	}

	// Load user with relationships
	return s.userRepo.FindByID(uint(user.ID))
}

func (s *UserService) Login(email, password string) (*domain.User, string, error) {
	if email == "" {
		return nil, "", errors.New("email is required")
	}
	if password == "" {
		return nil, "", errors.New("password is required")
	}

	user, err := s.userRepo.FindByEmail(email)
	if err != nil {
		return nil, "", errors.New("invalid credentials")
	}

	// Check password
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		return nil, "", errors.New("invalid credentials")
	}

	// Generate JWT token
	token, err := s.generateJWTToken(user)
	if err != nil {
		return nil, "", errors.New("failed to generate token")
	}

	return user, token, nil
}

func (s *UserService) GetByID(id uint) (*domain.User, error) {
	if id == 0 {
		return nil, errors.New("invalid user ID")
	}
	return s.userRepo.FindByID(id)
}

func (s *UserService) GetByEmail(email string) (*domain.User, error) {
	if email == "" {
		return nil, errors.New("email is required")
	}
	return s.userRepo.FindByEmail(email)
}

func (s *UserService) GetProfile(userID uint) (*domain.User, error) {
	return s.GetByID(userID)
}

func (s *UserService) UpdateProfile(userID uint, name, email string) (*domain.User, error) {
	if userID == 0 {
		return nil, errors.New("invalid user ID")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}
	if email == "" {
		return nil, errors.New("email is required")
	}

	user, err := s.userRepo.FindByID(userID)
	if err != nil {
		return nil, err
	}

	// Check if email is already taken by another user
	if user.Email != email {
		existingUser, err := s.userRepo.FindByEmail(email)
		if err == nil && existingUser.ID != user.ID {
			return nil, errors.New("email is already taken")
		}
	}

	user.Name = name
	user.Email = email

	err = s.userRepo.Update(user)
	if err != nil {
		return nil, err
	}

	return s.userRepo.FindByID(userID)
}

func (s *UserService) UpdateUser(adminUserID, targetUserID uint, name, email string) (*domain.User, error) {
	if adminUserID == 0 {
		return nil, errors.New("invalid admin user ID")
	}
	if targetUserID == 0 {
		return nil, errors.New("invalid target user ID")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}
	if email == "" {
		return nil, errors.New("email is required")
	}

	// Check if admin user exists and has admin privileges
	//adminUser, err := s.userRepo.FindByID(adminUserID)
	//if err != nil {
	//	return nil, errors.New("admin user not found")
	//}
	//
	//if adminUser.UserType == nil || !adminUser.UserType.IsAdmin {
	//	return nil, errors.New("insufficient permissions")
	//}

	// Get target user
	targetUser, err := s.userRepo.FindByID(targetUserID)
	if err != nil {
		return nil, errors.New("target user not found")
	}

	// Validate that target user is admin or member type only
	if targetUser.UserType == nil {
		return nil, errors.New("target user type not found")
	}

	//if !targetUser.UserType.IsAdmin && !targetUser.UserType.IsMember {
	//	return nil, errors.New("can only update admin and member users")
	//}

	// Check if email is already taken by another user
	if targetUser.Email != email {
		existingUser, err := s.userRepo.FindByEmail(email)
		if err == nil && existingUser.ID != targetUser.ID {
			return nil, errors.New("email is already taken")
		}
	}

	// Update user information
	targetUser.Name = name
	targetUser.Email = email

	err = s.userRepo.Update(targetUser)
	if err != nil {
		return nil, err
	}

	return s.userRepo.FindByID(targetUserID)
}

func (s *UserService) ChangePassword(userID uint, currentPassword, newPassword string) error {
	if userID == 0 {
		return errors.New("invalid user ID")
	}
	if currentPassword == "" {
		return errors.New("current password is required")
	}
	if newPassword == "" {
		return errors.New("new password is required")
	}
	if len(newPassword) < 6 {
		return errors.New("new password must be at least 6 characters")
	}

	user, err := s.userRepo.FindByID(userID)
	if err != nil {
		return err
	}

	// Verify current password
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(currentPassword))
	if err != nil {
		return errors.New("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return errors.New("failed to hash new password")
	}

	user.Password = string(hashedPassword)
	return s.userRepo.Update(user)
}

func (s *UserService) GetAllUsers(filter *ports.UserFilter) ([]*domain.User, error) {
	return s.userRepo.FindAll(filter)
}

func (s *UserService) DeleteUser(adminUserID, targetUserID uint) error {
	if adminUserID == 0 {
		return errors.New("invalid admin user ID")
	}
	if targetUserID == 0 {
		return errors.New("invalid target user ID")
	}

	// Check if admin user exists and is admin
	//adminUser, err := s.userRepo.FindByID(adminUserID)
	//if err != nil {
	//	return errors.New("admin user not found")
	//}
	//
	//if adminUser.UserType == nil || !adminUser.UserType.IsAdmin || !adminUser.UserType.IsMember {
	//	return errors.New("insufficient permissions")
	//}

	// Prevent admin from deleting themselves
	if adminUserID == targetUserID {
		return errors.New("cannot delete your own account")
	}

	// Check if target user exists
	_, err := s.userRepo.FindByID(targetUserID)
	if err != nil {
		return errors.New("target user not found")
	}

	return s.userRepo.Delete(targetUserID)
}

func (s *UserService) CreateUser(adminUserID uint, name, email, password string, userTypeID uint64) (*domain.User, error) {
	if adminUserID == 0 {
		return nil, errors.New("invalid admin user ID")
	}

	// Check if admin user exists and is admin
	adminUser, err := s.userRepo.FindByID(adminUserID)
	if err != nil {
		return nil, errors.New("admin user not found")
	}

	if adminUser.UserType == nil || !adminUser.UserType.IsAdmin {
		return nil, errors.New("insufficient permissions")
	}

	if name == "" {
		return nil, errors.New("name is required")
	}
	if email == "" {
		return nil, errors.New("email is required")
	}
	if password == "" {
		return nil, errors.New("password is required")
	}
	if len(password) < 6 {
		return nil, errors.New("password must be at least 6 characters")
	}

	// Check if user already exists
	_, err = s.userRepo.FindByEmail(email)
	if err == nil {
		return nil, errors.New("user with this email already exists")
	}

	// Verify user type exists
	_, err = s.userTypeRepo.FindByID(uint(userTypeID))
	if err != nil {
		return nil, errors.New("invalid user type")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, errors.New("failed to hash password")
	}

	user := &domain.User{
		Name:       name,
		Email:      email,
		Password:   string(hashedPassword),
		UserTypeID: userTypeID,
	}

	err = s.userRepo.Insert(user)
	if err != nil {
		return nil, err
	}

	// Load user with relationships
	return s.userRepo.FindByID(uint(user.ID))
}

func (s *UserService) CreateSaleUser(name, email, password string) (*domain.User, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if email == "" {
		return nil, errors.New("email is required")
	}
	if password == "" {
		return nil, errors.New("password is required")
	}
	if len(password) < 6 {
		return nil, errors.New("password must be at least 6 characters")
	}

	// Check if user already exists
	_, err := s.userRepo.FindByEmail(email)
	if err == nil {
		return nil, errors.New("user with this email already exists")
	}

	// Get sale user type
	saleTypeName := domain.UserTypeSale
	saleUserType, err := s.userTypeRepo.FindAll(&ports.UserTypeFilter{Name: &saleTypeName})
	if err != nil || len(saleUserType) == 0 {
		return nil, errors.New("sale user type not found")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, errors.New("failed to hash password")
	}

	user := &domain.User{
		Name:       name,
		Email:      email,
		Password:   string(hashedPassword),
		UserTypeID: uint64(saleUserType[0].ID),
	}

	err = s.userRepo.Insert(user)
	if err != nil {
		return nil, err
	}

	// Load user with relationships
	return s.userRepo.FindByID(uint(user.ID))
}

func (s *UserService) generateJWTToken(user *domain.User) (string, error) {
	claims := jwt.MapClaims{
		"user_id":      user.ID,
		"email":        user.Email,
		"user_type_id": user.UserTypeID,
		"exp":          time.Now().Add(24 * time.Hour).Unix(),
		"iat":          time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.jwtSecret))
}
