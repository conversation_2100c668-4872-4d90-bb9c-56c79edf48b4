package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateDomainRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	IsDefault   bool   `json:"is_default"`
	IsActive    bool   `json:"is_active"`
	ZoneID      string `json:"zone_id" validate:"required"`
	AccountID   string `json:"account_id" validate:"required"`
	AccountName string `json:"account_name" validate:"required"`
	NamespaceID uint64 `json:"namespace_id" validate:"required"`
}

type UpdateDomainRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	IsDefault   bool   `json:"is_default"`
	IsActive    bool   `json:"is_active"`
	ZoneID      string `json:"zone_id" validate:"required"`
	AccountID   string `json:"account_id" validate:"required"`
	AccountName string `json:"account_name" validate:"required"`
	NamespaceID uint64 `json:"namespace_id" validate:"required"`
	Index       int    `json:"index"`
}

type UpdateDomainStatusRequest struct {
	IsActive bool `json:"is_active"`
}

type SetDomainDefaultRequest struct {
	IsRedirect bool `json:"is_redirect"`
}

type DomainListItemResponse struct {
	ID          uint64                     `json:"id"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
	Name        string                     `json:"name"`
	IsDefault   bool                       `json:"is_default"`
	IsActive    bool                       `json:"is_active"`
	ZoneID      string                     `json:"zone_id"`
	AccountID   string                     `json:"account_id"`
	AccountName string                     `json:"account_name"`
	NamespaceID uint64                     `json:"namespace_id"`
	Index       int                        `json:"index"`
	Namespace   *NamespaceRelationResponse `json:"namespace,omitempty"`
}

type DomainDetailResponse struct {
	ID          uint64                     `json:"id"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
	Name        string                     `json:"name"`
	IsDefault   bool                       `json:"is_default"`
	IsActive    bool                       `json:"is_active"`
	ZoneID      string                     `json:"zone_id"`
	AccountID   string                     `json:"account_id"`
	AccountName string                     `json:"account_name"`
	NamespaceID uint64                     `json:"namespace_id"`
	Index       int                        `json:"index"`
	Namespace   *NamespaceRelationResponse `json:"namespace,omitempty"`
}

type DomainRelationResponse struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	IsDefault   bool   `json:"is_default"`
	IsActive    bool   `json:"is_active"`
	ZoneID      string `json:"zone_id"`
	AccountID   string `json:"account_id"`
	AccountName string `json:"account_name"`
	Index       int    `json:"index"`
}

// Convert response

func ToDomainListItemDTO(d *domain.Domain) *DomainListItemResponse {
	if d == nil {
		return nil
	}

	return &DomainListItemResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		IsDefault:   d.IsDefault,
		IsActive:    d.IsActive,
		ZoneID:      d.ZoneID,
		AccountID:   d.AccountID,
		AccountName: d.AccountName,
		NamespaceID: d.NamespaceID,
		Index:       d.Index,
		Namespace:   ToNamespaceRelationDTO(d.Namespace),
	}
}

func ToDomainDetailDTO(d *domain.Domain) *DomainDetailResponse {
	if d == nil {
		return nil
	}

	return &DomainDetailResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		IsDefault:   d.IsDefault,
		IsActive:    d.IsActive,
		ZoneID:      d.ZoneID,
		AccountID:   d.AccountID,
		AccountName: d.AccountName,
		NamespaceID: d.NamespaceID,
		Index:       d.Index,
		Namespace:   ToNamespaceRelationDTO(d.Namespace),
	}
}

func ToDomainRelationDTO(d *domain.Domain) *DomainRelationResponse {
	if d == nil {
		return nil
	}
	return &DomainRelationResponse{
		ID:          d.ID,
		Name:        d.Name,
		IsDefault:   d.IsDefault,
		IsActive:    d.IsActive,
		ZoneID:      d.ZoneID,
		AccountID:   d.AccountID,
		AccountName: d.AccountName,
		Index:       d.Index,
	}
}
