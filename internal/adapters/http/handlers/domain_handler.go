package handlers

import (
	utils "ops-api/pkg/utils/token"
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type DomainHandler struct {
	domainService ports.DomainService
}

func NewDomainHandler(domainService ports.DomainService) *DomainHandler {
	return &DomainHandler{
		domainService: domainService,
	}
}

func (h *DomainHandler) CreateDomain(c *fiber.Ctx) error {
	var req dto.CreateDomainRequest
	if err := c.<PERSON>er(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	domain, err := h.domainService.Create(
		req.Name,
		req.IsDefault,
		req.IsActive,
		req.ZoneID,
		req.AccountID,
		req.AccountName,
		req.NamespaceID,
	)
	if err != nil {
		if strings.Contains(err.<PERSON>rror(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Domain created successfully", dto.ToDomainDetailDTO(domain))
}

func (h *DomainHandler) GetDomains(c *fiber.Ctx) error {
	filter := &ports.DomainFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if isDefaultStr := c.Query("is_default"); isDefaultStr != "" {
		if isDefault, err := strconv.ParseBool(isDefaultStr); err == nil {
			filter.IsDefault = &isDefault
		}
	}

	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filter.IsActive = &isActive
		}
	}

	if namespaceIDStr := c.Query("namespace_id"); namespaceIDStr != "" {
		if namespaceID, err := strconv.ParseUint(namespaceIDStr, 10, 64); err == nil {
			filter.NamespaceID = &namespaceID
		}
	}

	domains, err := h.domainService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var responseList []*dto.DomainListItemResponse
	for _, domain := range domains {
		responseList = append(responseList, dto.ToDomainListItemDTO(domain))
	}

	return response.Success(c, fiber.StatusOK, "Domains retrieved successfully", responseList)
}

func (h *DomainHandler) GetDomain(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid dns ID")
	}

	domain, err := h.domainService.GetByID(id)
	if err != nil {
		return response.Error(c, fiber.StatusNotFound, "Domain not found")
	}

	return response.Success(c, fiber.StatusOK, "Domain retrieved successfully", dto.ToDomainDetailDTO(domain))
}

func (h *DomainHandler) UpdateDomain(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid dns ID")
	}

	var req dto.UpdateDomainRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	domain, err := h.domainService.Update(
		id,
		req.Name,
		req.IsDefault,
		req.IsActive,
		req.ZoneID,
		req.AccountID,
		req.AccountName,
		req.NamespaceID,
		req.Index,
	)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Domain updated successfully", dto.ToDomainDetailDTO(domain))
}

func (h *DomainHandler) UpdateDomainStatus(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid dns ID")
	}

	var req dto.UpdateDomainStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	domain, err := h.domainService.UpdateStatus(id, req.IsActive)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Domain status updated successfully", dto.ToDomainDetailDTO(domain))
}

func (h *DomainHandler) SetDomainDefault(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid dns ID")
	}

	var req dto.SetDomainDefaultRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Get user ID from authentication context
	userID := c.Locals("user_id")
	if userID == nil {
		return response.Error(c, fiber.StatusUnauthorized, "User authentication required")
	}

	userIDUint64, ok := userID.(uint64)
	if !ok {
		return response.Error(c, fiber.StatusUnauthorized, "Invalid user ID format")
	}

	accessToken := c.Get("Authorization")
	if accessToken == "" {
		return response.Error(c, fiber.StatusUnauthorized, "Authorization header is required")
	}

	// Remove "Bearer " prefix to get just the token
	if strings.HasPrefix(accessToken, "Bearer ") {
		accessToken = strings.TrimPrefix(accessToken, "Bearer ")
	}

	accessToken = utils.CleanToken(accessToken)

	domain, err := h.domainService.SetDefault(userIDUint64, id, accessToken, req.IsRedirect)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Domain set as default successfully", dto.ToDomainDetailDTO(domain))
}

func (h *DomainHandler) DeleteDomain(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid dns ID")
	}

	err = h.domainService.Delete(id)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Domain deleted successfully", nil)
}
